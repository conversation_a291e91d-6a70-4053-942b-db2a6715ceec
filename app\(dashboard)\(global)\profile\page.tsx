'use client'

import { MyAccountCard } from '@/features/profile/components/my-account-card'
import { useGlobalManager } from '@/hooks/use-context'
import { DottedSeparator } from '@/components/dotted-separator'

const ProfilePage = () => {

  const { user } = useGlobalManager();

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">My Profile</h1>
              <p className="text-muted-foreground">
                Here is where you can manage your Profile details.
              </p>

              <DottedSeparator />

            </div>

            {/* User details */}

            {user.role == 'admin' || user.role ==  'supervisor' ? (
              <div className="">Admin or supervisor details</div>
            ) : (
              <div className="">Student details</div>
            )}

          </div>
          
          <div>
            {/* Profile Card */}
            <MyAccountCard />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
