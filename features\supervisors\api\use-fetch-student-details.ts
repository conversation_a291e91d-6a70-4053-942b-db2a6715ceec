import { useQuery } from "@tanstack/react-query";

export const useFetchStudentDetails = (studentId: string) => {
    console.log('=== useFetchStudentDetails hook called ===', studentId);

    const query = useQuery({
        queryKey: ["supervisor-student-details", studentId],
        queryFn: async () => {
            console.log('=== useFetchStudentDetails queryFn executing ===');
            try {
                console.log('Fetching student details for ID:', studentId);

                const token = localStorage.getItem('iptms_token');
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/other/supervisors/students/${studentId}`, {
                    headers: {
                        'Authorization': token || ''
                    }
                });

                const data = await response.json();
                console.log('Student details response:', data);

                if (data.success && data.data) {
                    return data.data;
                }
                throw new Error(data.message || 'Failed to fetch student details');
            } catch (error: any) {
                console.error('Failed to fetch student details:', error);
                throw error;
            }
        },
        enabled: !!studentId, // Only run query if studentId exists
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });

    console.log('useFetchStudentDetails query state:', {
        isLoading: query.isLoading,
        isError: query.isError,
        data: query.data,
        error: query.error
    });

    return query;
};
